# 角色权限管理功能实现说明

## 功能概述

在团队管理页面新增了"角色权限管理"功能，实现了角色权限的增删改查操作。

## 实现内容

### 1. 类型定义 (src/views/team/types.ts)

新增了 `RolePermissionItem` 接口，定义了角色权限数据结构：

```typescript
export interface RolePermissionItem {
  id?: number
  roleId: number
  permissionId: number
  teamId: number
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
}
```

### 2. API接口 (src/views/team/api/index.ts)

新增了角色权限管理相关的API接口：

- `getRolePermissionList(params?: any)` - 获取角色权限列表
- `createRolePermission(data: RolePermissionItem)` - 创建角色权限
- `updateRolePermission(data: RolePermissionItem)` - 更新角色权限
- `deleteRolePermission(id: number)` - 删除角色权限
- `getRolePermissionDetail(id: number)` - 获取角色权限详情

接口路径：
- 列表：`/rolePermission/list`
- 创建：`/rolePermission/create`
- 更新：`/rolePermission/update`
- 删除：`/rolePermission/delete`
- 详情：`/rolePermission/detail`

### 3. 角色权限管理组件 (src/views/team/pages/teammange/RolePermissionTable.vue)

创建了角色权限管理表格组件，功能包括：

- **数据展示**：以表格形式展示角色权限列表
- **新增功能**：点击"新增角色权限"按钮可添加新记录
- **编辑功能**：支持行内编辑，点击"编辑"按钮进入编辑模式
- **删除功能**：使用 `el-popconfirm` 组件确认删除操作
- **分页功能**：支持分页显示，可调整每页显示数量
- **表单验证**：保存时验证必填字段（roleId、permissionId、teamId）

### 4. 团队管理主页面更新 (src/views/team/pages/teammange/index.vue)

在团队管理页面新增了"角色权限管理"tab：

- 在 `tabs` 数组中添加了新的tab项
- 在内容区域添加了角色权限管理组件的显示逻辑
- 导入了 `RolePermissionTable` 组件

## 数据结构参考

基于 `rolepermission.json` 文件的数据结构：

```json
{
  "code": 1,
  "message": "success",
  "data": {
    "currentPage": 1,
    "pageSize": 10,
    "total": 2,
    "data": [
      {
        "id": 8001,
        "roleId": 1,
        "permissionId": 9001,
        "teamId": 1001,
        "createTime": "2025-07-15 10:00:00",
        "createBy": "admin",
        "updateTime": "2025-07-20 16:00:00",
        "updateBy": "admin"
      }
    ]
  }
}
```

## 功能特点

1. **表格形式展示**：参考了 `DataPermissionTable.vue` 组件的设计风格
2. **行内编辑**：支持直接在表格中编辑数据
3. **确认删除**：使用 `el-popconfirm` 组件提供删除确认
4. **响应式设计**：适配不同屏幕尺寸
5. **错误处理**：包含完整的错误处理和用户提示
6. **数据验证**：保存前验证必填字段

## 使用方法

1. 进入团队管理页面
2. 点击左侧"角色权限管理"tab
3. 可以进行以下操作：
   - 查看角色权限列表
   - 点击"新增角色权限"添加新记录
   - 点击"编辑"修改现有记录
   - 点击"删除"删除记录（需确认）
   - 使用分页功能浏览更多数据

## 注意事项

- 所有ID字段（roleId、permissionId、teamId）都是必填的数字类型
- 删除操作需要用户确认
- 新增和编辑时会进行字段验证
- 操作成功后会自动刷新列表数据
