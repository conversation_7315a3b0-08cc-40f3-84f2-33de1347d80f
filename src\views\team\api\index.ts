import { defHttp } from '/@/utils/axios/index'
import { DataPermissionItem, RolePermissionItem } from '/@/views/team/types'

enum Api {
  DataPermissionList = '/dataPermission/list',
  DataPermissionCreate = '/dataPermission/create',
  DataPermissionUpdate = '/dataPermission/update',
  DataPermissionDelete = '/dataPermission/delete',
  DataPermissionDetail = '/dataPermission/detail',
  RolePermissionList = '/rolePermission/list',
  RolePermissionCreate = '/rolePermission/create',
  RolePermissionUpdate = '/rolePermission/update',
  RolePermissionDelete = '/rolePermission/delete',
  RolePermissionDetail = '/rolePermission/detail'
}

// 获取数据权限列表
export const getDataPermissionList = (params?: any) => {
  return defHttp.get({ url: Api.DataPermissionList, params })
}

// 创建数据权限
export const createDataPermission = (data: DataPermissionItem) => {
  return defHttp.post({ url: Api.DataPermissionCreate, data })
}

// 更新数据权限
export const updateDataPermission = (data: DataPermissionItem) => {
  return defHttp.post({ url: Api.DataPermissionUpdate, data })
}

// 删除数据权限
export const deleteDataPermission = (id: number) => {
  return defHttp.post({ url: Api.DataPermissionDelete, data: { id } })
}

// 获取数据权限详情
export const getDataPermissionDetail = (id: number) => {
  return defHttp.get({ url: Api.DataPermissionDetail, params: { id } })
}

// 角色权限管理相关接口

// 获取角色权限列表
export const getRolePermissionList = (params?: any) => {
  return defHttp.get({ url: Api.RolePermissionList, params })
}

// 创建角色权限
export const createRolePermission = (data: RolePermissionItem) => {
  return defHttp.post({ url: Api.RolePermissionCreate, data })
}

// 更新角色权限
export const updateRolePermission = (data: RolePermissionItem) => {
  return defHttp.post({ url: Api.RolePermissionUpdate, data })
}

// 删除角色权限
export const deleteRolePermission = (id: number) => {
  return defHttp.post({ url: Api.RolePermissionDelete, data: { id } })
}

// 获取角色权限详情
export const getRolePermissionDetail = (id: number) => {
  return defHttp.get({ url: Api.RolePermissionDetail, params: { id } })
}

