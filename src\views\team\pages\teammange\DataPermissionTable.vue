<template>
  <div class="data-annotation-container" ref="containerRef">
    <!-- 左侧数据源树 -->
    <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
      <div class="panel-header">
        <h3>数据库</h3>
        <!-- 实现刷新功能 -->
        <el-icon style="cursor:pointer;" @click="refreshTree">
          <component :is="Refresh" />
        </el-icon>
      </div>
      <div class="tree-container">
        <el-tree
          v-loading="treeLoading"
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          node-key="id"
          :expand-on-click-node="true"
          :highlight-current="true"
          :current-node-key="currentNodeKey"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <Iconfont
                v-if="data?.sqlType && getDatabaseIcon(data.sqlType)"
                class="node-icon-iconfont"
                :name="getDatabaseIcon(data.sqlType) || ''"
              />
              
              <el-icon  v-else class="node-icon">
                <!-- {{ data.type }} -->
                <component v-if="data.type == 'database'" color="#337ecc" :is="getNodeIcon(data.type)" />
                <component v-if="data.type == 'table'" color="#529b2e" :is="getNodeIcon(data.type)" />
                <component v-if="data.type == 'schema'" color="#b88230" :is="getNodeIcon(data.type)" />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 分割线 -->
    <div
      class="splitter"
      :class="{ 'splitter-resizing': isResizing }"
      @mousedown="startResize"
    ></div>

    <!-- 右侧数据权限表格 -->
    <div class="right-panel" :style="{ width: rightPanelWidth + 'px' }">
      
      <div class="table-container">
        <!-- 数据权限表格 -->
        <!-- <el-card shadow="never" class="table-card"> -->
          <!-- <template #header></template> -->
            <div class="card-header">
              <span>数据权限列表</span>
              <el-button v-if="currentNodeKey && currentTableNode?.type === 'table'" type="primary" @click="handleAdd">新增数据权限</el-button>
            </div>
          
          
          <el-table 
            :data="dataPermissionList" 
            v-loading="tableLoading"
            border
            style="width: 100%">
            <el-table-column prop="tableName" label="表名" min-width="120" />
            <el-table-column prop="permissionName" label="数据权限名称" min-width="150">
              <template #default="{ row }">
                <el-input 
                  v-if="row.isEditing" 
                  v-model="row.permissionName" 
                  placeholder="请输入权限名称" 
                />
                <span v-else>{{ row.permissionName }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="description" label="数据权限简介" min-width="200">
              <template #default="{ row }">
                <el-input 
                  v-if="row.isEditing" 
                  v-model="row.description"
                  placeholder="请输入权限简介" 
                />
                <span v-else>{{ row.description }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="createTime" label="创建时间" min-width="150" />
            <el-table-column prop="updateTime" label="更新时间" min-width="150" /> -->
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div v-if="row.isEditing">
                  <el-button type="primary" link @click="handleSave(row)">保存</el-button>
                  <el-button type="primary" link @click="handleCancel(row)">取消</el-button>
                </div>
                <div v-else>
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        <!-- </el-card> -->
        
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  Coin, 
  Folder, 
  Document, 
  Grid 
} from '@element-plus/icons-vue'
import { AxiosApiService } from './../../../chatagent/api/axios-api-service'
import Iconfont from '/@/components/Iconfont.vue'
import {
  getDataPermissionList,
  createDataPermission,
  updateDataPermission,
  deleteDataPermission
} from '/@/views/team/api/index'
import { DataPermissionItem } from '/@/views/team/types'

// 树形数据相关
interface TreeNode {
  id: string
  label: string
  type: 'datasource' | 'database' | 'schema' | 'table'
  children?: TreeNode[]
  isLeaf?: boolean
  dataSourceId?: number
  dataSourceName?: string
  databaseType?: string
  databaseName?: string
  schemaName?: string
  tableName?: string
}

// 表格列数据
interface TableColumn {
  columnName: string
  originalComment?: string
  columnCommentAlias?: string
  functionExamples: string
  columnExampleData: string
  sortOrder?: number
  foreignTableName?: string
  foreignColumnName?: string
  isEditing?: boolean
  originalData?: any
}

// 响应式数据
const treeRef = ref()
const treeData = ref<TreeNode[]>([])
const tableColumns = ref<TableColumn[]>([])
const loading = ref(false)
const selectedTableName = ref('')
const treeLoading = ref(false)
// 新增：表AI注释字段
const tableCommentAlias = ref('')
// 面板宽度控制
const leftPanelWidth = ref(200)
const containerRef = ref<HTMLElement>()
const rightPanelWidth = computed(() => {
  if (containerRef.value) {
    return containerRef.value.clientWidth - leftPanelWidth.value - 6 // 6px 是分割线宽度
  }
  return window.innerWidth - leftPanelWidth.value - 6
})
const isResizing = ref(false)

// 数据权限相关数据
const dataPermissionList = ref<(DataPermissionItem & { isEditing?: boolean, originalData?: DataPermissionItem })[]>([])
const tableLoading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 节流函数
const throttle = (func: Function, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0
  return function (this: any, ...args: any[]) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

// 树形配置
const treeProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'isLeaf'
}

// 获取节点图标
const getNodeIcon = (type: string) => {
  switch (type) {
    case 'datasource':
      return Folder
    case 'database':
      return Coin
    case 'schema':
      return Folder
    case 'table':
      return Grid
    default:
      return Document
  }
}
const refreshTree = async () => {
  treeLoading.value = true;
  treeData.value = [];
  // 清除选中状态
  currentNodeKey.value = undefined;
  selectedTableName.value = '';
  currentTableNode.value = null;
  tableColumns.value = [];

  try {
    const dataSources = await AxiosApiService.getDataSourceList();
    treeData.value = dataSources.map(ds => ({
      id: `ds_${ds.id}`,
      label: ds.name,
      type: 'datasource',
      dataSourceId: ds.id,
      dataSourceName: ds.name,
      databaseType: ds.type,
      sqlType: ds.type,
      isLeaf: false
    }));
  } catch (error) {
    console.error('刷新数据源失败:', error);
  } finally {
    treeLoading.value = false;
  }
}
// 数据库类型列表（与数据源管理页面保持一致）
const databaseTypeList = [
  {
    label: 'MySQL',
    value: 'mysql',
    icon: 'mysql',
  },
  {
    label: 'H2',
    value: 'h2',
    icon: 'h2',
  },
  {
    label: 'Oracle',
    value: 'oracle',
    icon: 'oracle',
  },
  {
    label: 'PostgreSql',
    value: 'postgresql',
    icon: 'postgresql',
  },
  {
    label: 'SQLServer',
    icon: 'sqlserver',
    value: 'sqlserver',
  },
  {
    label: 'SQLite',
    icon: 'sqlite',
    value: 'sqlite',
  },
  {
    label: 'Mariadb',
    value: 'mariadb',
    icon: 'rds_mariadb',
  },
  {
    label: 'ClickHouse',
    value: 'clickhouse',
    icon: 'clickhouse-yunshujukuClickHouse',
  },
  {
    label: 'DM',
    value: 'dm',
    icon: 'dameng1',
  },
  {
    label: 'Presto',
    value: 'presto',
    icon: 'presto_sql',
  },
  {
    label: 'DB2',
    value: 'db2',
    icon: 'shujukuleixingtubiao-kuozhan-',
  },
  {
      label: 'Hive',
      icon: 'HIVE',
      value: 'hive',
    },
    {
      label: 'KingBase',
      icon: 'Kingbase',
      value: 'kingbase',
    },
    {
      label: 'MongoDB',
      icon: 'mongodb',
      value: 'mongodb',
    },

    {
      label: 'Timeplus',
      value: 'timeplus',
      icon: 'clickhouse-yunshujukuClickHouse',
    }
]

// 获取数据库类型对应的图标名称
const getDatabaseIcon = (sqlType: string) => {
  if (!sqlType) return null

  const lowerType = sqlType.toLowerCase()
  const foundItem = databaseTypeList.find((item) => item.value === lowerType)
  return foundItem ? foundItem.icon : null
}

// 懒加载树节点
const loadNode = async (node: any, resolve: Function) => {
  if (node.level === 0) {
    treeLoading.value = true;
    // 加载数据源
    try {
      const dataSources = await AxiosApiService.getDataSourceList()
      const nodes = dataSources.map(ds => ({
        id: `ds_${ds.id}`,
        label: ds.name,
        type: 'datasource' as const,
        dataSourceId: ds.id,
        dataSourceName: ds.name,
        databaseType: ds.type,
        sqlType: ds.type,
        isLeaf: false
      }))
      resolve(nodes)
      treeLoading.value = false;
    } catch (error) {
      console.error('加载数据源失败:', error)
      treeLoading.value = false;
      resolve()
      
    }
  } else if (node.data.type === 'datasource') {
    // 加载数据库
    try {
      const databases = await AxiosApiService.getDatabaseList(node.data.dataSourceId)
      const nodes = databases.map(db => ({
        id: `db_${node.data.dataSourceId}_${db.name}`,
        label: db.name,
        type: 'database' as const,
        dataSourceId: node.data.dataSourceId,
        dataSourceName: node.data.dataSourceName,
        databaseType: node.data.databaseType,
        databaseName: db.name,
        isLeaf: false
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载数据库失败:', error)
      console.log('node.data:', node)
      node.loading = false
      resolve()
    }
  } else if (node.data.type === 'database') {
    // 加载Schema
    try {
      const schemas = await AxiosApiService.getSchemaList(
        node.data.dataSourceId, 
        node.data.databaseName
      )
      const nodes = schemas.map(schema => ({
        id: `schema_${node.data.dataSourceId}_${node.data.databaseName}_${schema.name}`,
        label: schema.name,
        type: 'schema' as const,
        dataSourceId: node.data.dataSourceId,
        dataSourceName: node.data.dataSourceName,
        databaseType: node.data.databaseType,
        databaseName: node.data.databaseName,
        schemaName: schema.name,
        isLeaf: false
      }))
      resolve(nodes)
    } catch (error) {
      console.error('加载Schema失败:', error)
      node.loading = false
      resolve()
    }
  } else if (node.data.type === 'schema') {
    // 加载数据表
    try {
      const tables = await AxiosApiService.getTableList(
        node.data.dataSourceId,
        node.data.databaseName,
        node.data.schemaName
      )
      console.log('tables:', tables)
      const tableList = Array.isArray(tables) ? tables : ((tables as any)?.data || [])
      const nodes = tableList.map((table: any) => ({
        id: `table_${node.data.dataSourceId}_${node.data.databaseName}_${node.data.schemaName}_${table.name}`,
        label: table.name,
        type: 'table' as const,
        dataSourceId: node.data.dataSourceId,
        dataSourceName: node.data.dataSourceName,
        databaseType: node.data.databaseType,
        databaseName: node.data.databaseName,
        schemaName: node.data.schemaName,
        tableName: table.name,
        isLeaf: true
      }))
      // debugger
      resolve(nodes)
    } catch (error) {
      console.error('加载数据表失败:', error)
      node.loading = false
      resolve()
    }
  }
}

// 处理节点点击
const handleNodeClick = async (data: TreeNode) => {
  // 设置当前选中的节点key，保持高亮状态
  currentNodeKey.value = data.id

  // 当点击数据库或模式节点时，获取数据权限列表
  if (data.type === 'database' || data.type === 'schema') {
    await fetchDataPermissionList(data)
  }

  // if (data.type === 'table') {
    selectedTableName.value = data.tableName || ''
    currentTableNode.value = data
    // await loadTableColumns(data)
  // }
}

// 获取数据权限列表
const fetchDataPermissionList = async (nodeData: TreeNode) => {
  tableLoading.value = true
  try {
    const params = {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      databaseName: nodeData.databaseName,
      schemaName: nodeData.schemaName,
      tableName: nodeData.tableName
    }
    
    const res = await getDataPermissionList(params)
    dataPermissionList.value = (res.data.data || []).map(item => ({
      ...item,
      isEditing: false
    }))
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取数据权限列表失败:', error)
    ElMessage.error('获取数据权限列表失败')
    dataPermissionList.value = []
    pagination.total = 0
  } finally {
    tableLoading.value = false
  }
}

// 当前选中的表节点
const currentTableNode = ref<TreeNode | null>(null)
// 当前选中的节点key，用于保持高亮状态
const currentNodeKey = ref<string | number | undefined>(undefined)


// 处理调整大小的核心逻辑
const doResize = (e: MouseEvent) => {
  if (isResizing.value && containerRef.value) {
    const containerRect = containerRef.value.getBoundingClientRect()
    const newWidth = e.clientX - containerRect.left
    const minWidth = 200
    const maxWidth = containerRect.width - 400 // 保留右侧至少400px

    if (newWidth >= minWidth && newWidth <= maxWidth) {
      leftPanelWidth.value = newWidth
    }
  }
}

// 节流处理的 resize 函数
const handleResize = throttle(doResize, 16) // 约60fps

// 开始调整大小
const startResize = (e: MouseEvent) => {
  isResizing.value = true

  // 禁用文本选择
  document.body.style.userSelect = 'none'
  document.body.style.cursor = 'col-resize'

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false

  // 恢复文本选择和光标
  document.body.style.userSelect = ''
  document.body.style.cursor = ''

  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 窗口大小变化处理
const handleWindowResize = throttle(() => {
  if (containerRef.value) {
    const containerWidth = containerRef.value.clientWidth
    const maxLeftWidth = containerWidth - 400
    if (leftPanelWidth.value > maxLeftWidth) {
      leftPanelWidth.value = Math.max(200, maxLeftWidth)
    }
  }
}, 100)

// 组件挂载时添加窗口 resize 监听
onMounted(() => {
  window.addEventListener('resize', handleWindowResize)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  window.removeEventListener('resize', handleWindowResize)
})

// 处理新增
const handleAdd = () => {
  // 在列表开头添加一个新行
  const newNode = currentTableNode.value
  if (!newNode) {
    ElMessage.warning('请选择一个表节点')
    return
  }
  
  const newItem: DataPermissionItem & { isEditing?: boolean, originalData?: DataPermissionItem } = {
    permissionName: '',
    description: '',
    databaseName: newNode.databaseName || '',
    schemaName: newNode.schemaName || '',
    tableName: newNode.tableName || '',
    isEditing: true
  }
  
  dataPermissionList.value.unshift(newItem)
}

// 处理编辑
const handleEdit = (row: DataPermissionItem & { isEditing?: boolean, originalData?: DataPermissionItem }) => {
  row.isEditing = true
  row.originalData = { ...row }
}

// 处理保存
const handleSave = async (row: DataPermissionItem & { isEditing?: boolean, originalData?: DataPermissionItem }) => {
  try {
    // 验证必填字段
    if (!row.permissionName || !row.description) {
      ElMessage.warning('请填写必填字段')
      return
    }
    
    if (row.id) {
      // 更新现有项
      await updateDataPermission(row)
      ElMessage.success('更新成功')
    } else {
      // 创建新项
      const res = await createDataPermission(row)
      // 更新ID
      row.id = res.data.id
      ElMessage.success('新增成功')
    }
    
    row.isEditing = false
    row.originalData = undefined
    
    // 重新获取数据
    if (currentTableNode.value) {
      await fetchDataPermissionList(currentTableNode.value)
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理取消
const handleCancel = (row: DataPermissionItem & { isEditing?: boolean, originalData?: DataPermissionItem }) => {
  if (row.id) {
    // 编辑已存在的项，恢复原始数据
    Object.assign(row, row.originalData)
    row.isEditing = false
    row.originalData = undefined
  } else {
    // 新增项，直接从列表中移除
    const index = dataPermissionList.value.findIndex(item => item === row)
    if (index > -1) {
      dataPermissionList.value.splice(index, 1)
    }
  }
}

// 处理删除
const handleDelete = async (row: DataPermissionItem) => {
  try {
    await ElMessageBox.confirm('确认删除该数据权限吗？', '提示', {
      type: 'warning'
    })
    
    await deleteDataPermission(row.id!)
    ElMessage.success('删除成功')
    // 重新获取数据
    if (currentTableNode.value) {
      await fetchDataPermissionList(currentTableNode.value)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页相关处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  if (currentTableNode.value) {
    fetchDataPermissionList(currentTableNode.value)
  }
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  if (currentTableNode.value) {
    fetchDataPermissionList(currentTableNode.value)
  }
}
</script>

<style scoped lang="scss">
.data-annotation-container {
  display: flex;
  height: calc(100vh - 64px);
  background-color: #f5f7fa;
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  margin-left: -20px;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 400px;
  overflow-y: auto;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  // gap: 8px;
}

.tree-container {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}
.node-icon-iconfont{
  font-size: 14px;
  margin-right: 6px;
  color: var(--el-color-primary);
}
.tree-node {
  display: flex;
  align-items: center;
  
  .node-icon {
    margin-right: 8px;
    color: #606266;
  }
  
  .node-label {
    font-size: 14px;
    color: #303133;
  }
}

.splitter {
  width: 6px;
  background-color: #e6e6e6;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #409eff;
  }

  &:active {
    background-color: #337ecc;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    background-color: #fff;
    border-radius: 1px;
    transition: all 0.2s ease;
  }

  &:hover::after {
    height: 30px;
    width: 3px;
  }

  &.splitter-resizing {
    background-color: #337ecc;

    &::after {
      height: 40px;
      width: 4px;
      background-color: #fff;
      box-shadow: 0 0 4px rgba(51, 126, 204, 0.5);
    }
  }
}

.table-container {
  flex: 1;
  padding: 16px;
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.column-info {
  .column-name {
    font-weight: 600;
    color: #303133;
  }
  
  .column-comment {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.editable-cell {
  min-height: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: #f5f7fa;
  }

  .cell-content {
    color: #303133;
  }

  .cell-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
}

.sort-order {
  color: #606266;
  font-weight: 500;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

// 鼠标悬停时的背景色
:deep(.el-tree-node__content:hover) {
  background-color: #ecf5ff !important;
}

// 当前选中节点的背景色（保持选中状态，即使失去焦点）
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}

// 确保焦点状态下的选中节点背景色
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}

// 确保选中节点在失去焦点后仍保持背景色
:deep(.el-tree-node.is-current > .el-tree-node__content),
:deep(.el-tree-node.is-current:focus > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}
// :deep(.el-table .cell) {
//   padding: 8px 12px;
// }

// :deep(.el-table th) {
//   background-color: #fafafa;
//   color: #303133;
//   font-weight: 600;
// }

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #409eff;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

.panel-header .header-actions .el-button {
  border-radius: 4px;
}

.panel-header .header-actions .el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.panel-header .header-actions .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}
</style>