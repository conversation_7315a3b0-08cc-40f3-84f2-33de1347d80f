<template>
  <div class="ai-assistant-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <h3 class="page-title">AI助手中心</h3>
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索助手"
          prefix-icon="Search"
          clearable
          @input="handleSearch"
        />
      </div>
    </div>

    <!-- 助手列表 -->
    <div class="assistant-grid" v-loading="loading">
      <div
        v-for="assistant in filteredAssistants"
        :key="assistant.planTemplateId"
        class="assistant-card"
        @click="startChat(assistant.planTemplateId, assistant.title)"
      >
        <!-- 左侧图标 -->
        <div class="assistant-icon">
          {{ getFirstChar(assistant.title) }}
        </div>

        <!-- 右侧内容 -->
        <div class="assistant-content">
          <div class="assistant-info">
            <h3 class="assistant-title">{{ assistant.title }}</h3>
            <p class="assistant-description">{{ assistant.description }}</p>
          </div>
          <div class="assistant-actions">
            <el-button text bg type="" @click.stop="startChat(assistant.planTemplateId, assistant.title)">
              开始对话
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && filteredAssistants.length === 0" class="empty-state">
      <el-empty description="暂无可用的AI助手" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AxiosApiService, type Agent } from '../../chatagent/api/axios-api-service'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const assistants = ref<Agent[]>([])

// 计算属性 - 过滤后的助手列表
const filteredAssistants = computed(() => {
  if (!searchKeyword.value) {
    return assistants.value
  }
  return assistants.value.filter(assistant =>
    assistant.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    assistant.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 获取标题第一个汉字
const getFirstChar = (title: string): string => {
  return title.charAt(0).toUpperCase()
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}

// 开始对话
const startChat = (assistantId: string, assistantName: string) => {
  console.log(assistantId,assistantName)
  router.push(`/chatagent/chat?id=${assistantId}&name=${assistantName}`)
}

// 加载助手列表
const loadAssistants = async () => {
  loading.value = true
  try {
    assistants.value = await AxiosApiService.getAllAgents()
    console.log(assistants.value) 
  } catch (error) {
    ElMessage.error('加载AI助手列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAssistants()
})
</script>

<style scoped lang="scss">
.ai-assistant-center {
  padding: 24px;
  height: calc(100vh - 62px);
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e6e8ee;
    margin-right: 8px;
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .search-box {
      width: 300px;
    }
  }

  .assistant-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
  }

  .assistant-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .assistant-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #1a5efe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .assistant-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 80px;

      .assistant-info {
        .assistant-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }

        .assistant-description {
          font-size: 13px;
          color: #6b7280;
          line-height: 1.5;
          margin: 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }

      .assistant-actions {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-assistant-center {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .search-box {
        width: 100%;
      }
    }

    .assistant-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .assistant-card {
      padding: 16px;

      .assistant-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
      }

      .assistant-content {
        .assistant-info {
          .assistant-title {
            font-size: 16px;
          }

          .assistant-description {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
