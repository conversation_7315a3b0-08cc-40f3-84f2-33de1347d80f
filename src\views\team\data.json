{"code": 1, "message": "success", "data": {"currentPage": 1, "pageSize": 10, "total": 2, "data": [{"id": 9001, "permissionName": "统计内蒙古极速鲜邮件数量", "description": "限定地域与产品，聚合邮件数据", "teamId": 1001, "datasourceId": 3001, "databaseName": "mail_center", "schemaName": "public", "tableName": "poc_data", "conditionExpression": "SELECT SUM(state_mails) FROM (SELECT state_mails, is_deleted, product_name FROM poc_data WHERE unseal_province_name IN ('内蒙古自治区')) AS t WHERE is_deleted = '0' AND product_name = '国内极速鲜'", "createTime": "2025-07-10 09:00:00", "createBy": "admin", "updateTime": "2025-08-01 15:30:00", "updateBy": "admin", "enable": "1"}, {"id": 9002, "permissionName": "计算测试表活跃用户数量", "description": "嵌套过滤，聚合活跃用户", "teamId": 1002, "datasourceId": 3002, "databaseName": "user_analytics", "schemaName": "analysis", "tableName": "user_events", "conditionExpression": "SELECT COUNT(*) FROM (SELECT user_id, status FROM user_events WHERE event_type = 'login') AS t WHERE status = 'active'", "createTime": "2025-07-12 10:15:00", "createBy": "system", "updateTime": "2025-07-28 11:45:00", "updateBy": "system", "enable": "1"}]}}