<template>
  <div class="role-permission-container">
        <div class="card-header">
          <span>角色权限列表</span>
          <el-button type="primary" @click="handleAdd">新增角色权限</el-button>
        </div>
      
      <el-table 
        :data="rolePermissionList" 
        v-loading="tableLoading"
        border
        style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="roleId" label="角色ID" min-width="100">
          <template #default="{ row }">
            <el-input 
              v-if="row.isEditing" 
              v-model.number="row.roleId" 
              placeholder="请输入角色ID" 
              type="number"
            />
            <span v-else>{{ row.roleId }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="permissionId" label="权限ID" min-width="100">
          <template #default="{ row }">
            <el-input 
              v-if="row.isEditing" 
              v-model.number="row.permissionId"
              placeholder="请输入权限ID" 
              type="number"
            />
            <span v-else>{{ row.permissionId }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="teamId" label="团队ID" min-width="100">
          <template #default="{ row }">
            <el-input 
              v-if="row.isEditing" 
              v-model.number="row.teamId"
              placeholder="请输入团队ID" 
              type="number"
            />
            <span v-else>{{ row.teamId }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" min-width="150" />
        <el-table-column prop="createBy" label="创建人" min-width="100" />
        <el-table-column prop="updateTime" label="更新时间" min-width="150" />
        <el-table-column prop="updateBy" label="更新人" min-width="100" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div v-if="row.isEditing">
              <el-button type="primary" link @click="handleSave(row)">保存</el-button>
              <el-button type="primary" link @click="handleCancel(row)">取消</el-button>
            </div>
            <div v-else>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-popconfirm
                title="确认删除吗?"
                confirm-button-text="确认"
                cancel-button-text="取消"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getRolePermissionList,
  createRolePermission,
  updateRolePermission,
  deleteRolePermission
} from '/@/views/team/api/index'
import { RolePermissionItem } from '/@/views/team/types'

// 响应式数据
const rolePermissionList = ref<(RolePermissionItem & { isEditing?: boolean, originalData?: RolePermissionItem })[]>([])
const tableLoading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取角色权限列表
const fetchRolePermissionList = async () => {
  tableLoading.value = true
  try {
    const params = {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize
    }
    
    const res = await getRolePermissionList(params)
    rolePermissionList.value = (res.data.data || []).map(item => ({
      ...item,
      isEditing: false
    }))
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取角色权限列表失败:', error)
    ElMessage.error('获取角色权限列表失败')
    rolePermissionList.value = []
    pagination.total = 0
  } finally {
    tableLoading.value = false
  }
}

// 处理新增
const handleAdd = () => {
  const newItem: RolePermissionItem & { isEditing?: boolean, originalData?: RolePermissionItem } = {
    roleId: 0,
    permissionId: 0,
    teamId: 0,
    isEditing: true
  }
  
  rolePermissionList.value.unshift(newItem)
}

// 处理编辑
const handleEdit = (row: RolePermissionItem & { isEditing?: boolean, originalData?: RolePermissionItem }) => {
  row.isEditing = true
  row.originalData = { ...row }
}

// 处理保存
const handleSave = async (row: RolePermissionItem & { isEditing?: boolean, originalData?: RolePermissionItem }) => {
  try {
    // 验证必填字段
    if (!row.roleId || !row.permissionId || !row.teamId) {
      ElMessage.warning('请填写必填字段')
      return
    }
    
    if (row.id) {
      // 更新现有项
      await updateRolePermission(row)
      ElMessage.success('更新成功')
    } else {
      // 创建新项
      const res = await createRolePermission(row)
      // 更新ID
      row.id = res.data.id
      ElMessage.success('新增成功')
    }
    
    row.isEditing = false
    row.originalData = undefined
    
    // 重新获取数据
    await fetchRolePermissionList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理取消
const handleCancel = (row: RolePermissionItem & { isEditing?: boolean, originalData?: RolePermissionItem }) => {
  if (row.id) {
    // 编辑已存在的项，恢复原始数据
    Object.assign(row, row.originalData)
    row.isEditing = false
    row.originalData = undefined
  } else {
    // 新增项，直接从列表中移除
    const index = rolePermissionList.value.findIndex(item => item === row)
    if (index > -1) {
      rolePermissionList.value.splice(index, 1)
    }
  }
}

// 处理删除
const handleDelete = async (row: RolePermissionItem) => {
  try {
    await deleteRolePermission(row.id!)
    ElMessage.success('删除成功')
    // 重新获取数据
    await fetchRolePermissionList()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 分页相关处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchRolePermissionList()
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  fetchRolePermissionList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRolePermissionList()
})
</script>

<style scoped lang="scss">
.role-permission-container {
  padding-top: 20px;
  height: calc(100vh - 64px);
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}
</style>
