export interface DataPermissionItem {
  id?: number
  permissionName: string
  description: string
  teamId?: number
  datasourceId?: number
  databaseName: string
  schemaName: string
  tableName: string
  conditionExpression?: string
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  enable?: string
}

export interface RolePermissionItem {
  id?: number
  roleId: number
  permissionId: number
  teamId: number
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
}