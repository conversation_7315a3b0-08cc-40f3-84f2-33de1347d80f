// 多系列图表测试用例
// 这个文件展示了如何使用新的多系列图表功能

// 测试数据 - 新的多系列格式
const multiSeriesData = {
  chartType: "line",
  select_data: [
    {
      date: "2023-01-01",
      total_mails: 100,
      overrated_mails: 20,
      overrated_24_hours: 5
    },
    {
      date: "2023-01-02", 
      total_mails: 120,
      overrated_mails: 25,
      overrated_24_hours: 8
    },
    {
      date: "2023-01-03",
      total_mails: 90,
      overrated_mails: 15,
      overrated_24_hours: 3
    },
    {
      date: "2023-01-04",
      total_mails: 150,
      overrated_mails: 30,
      overrated_24_hours: 12
    },
    {
      date: "2023-01-05",
      total_mails: 110,
      overrated_mails: 22,
      overrated_24_hours: 7
    }
  ],
  XFields: "date",
  series: {
    dataFields: [
      "total_mails",
      "overrated_mails", 
      "overrated_24_hours"
    ]
  },
  title: "邮件统计多维度分析"
}

// 测试数据 - 兼容原有单系列格式
const singleSeriesData = {
  chartType: "line",
  select_data: [
    { date: "2023-01-01", total_mails: 100 },
    { date: "2023-01-02", total_mails: 120 },
    { date: "2023-01-03", total_mails: 90 },
    { date: "2023-01-04", total_mails: 150 },
    { date: "2023-01-05", total_mails: 110 }
  ],
  XFields: "date",
  YFields: "total_mails",
  title: "邮件总数趋势"
}

// 测试数据 - 柱状图多系列
const barMultiSeriesData = {
  chartType: "bar",
  select_data: [
    {
      category: "产品A",
      sales: 1200,
      profit: 300,
      cost: 900
    },
    {
      category: "产品B",
      sales: 800,
      profit: 200,
      cost: 600
    },
    {
      category: "产品C",
      sales: 1500,
      profit: 450,
      cost: 1050
    }
  ],
  XFields: "category",
  series: {
    dataFields: ["sales", "profit", "cost"]
  },
  title: "产品销售分析"
}

// 模拟测试函数
function testMultiSeriesChart() {
  console.log("测试多系列图表功能...")
  
  // 测试数据字段提取
  const { data, chartType, xFields, series } = multiSeriesData
  const dataFields = series && series.dataFields ? series.dataFields : []
  
  console.log("数据字段:", dataFields)
  console.log("预期系列数量:", dataFields.length)
  
  // 模拟颜色分配
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', 
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]
  
  dataFields.forEach((field, index) => {
    console.log(`系列 ${index + 1}: ${field} - 颜色: ${colors[index % colors.length]}`)
  })
  
  return true
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    multiSeriesData,
    singleSeriesData,
    barMultiSeriesData,
    testMultiSeriesChart
  }
} else {
  // 浏览器环境
  console.log("多系列图表测试数据已准备就绪")
  testMultiSeriesChart()
}
