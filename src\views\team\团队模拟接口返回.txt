团队接口模拟返回：

{
  "code": 1,
  "message": "success",
  "data": {
    "currentPage": 1,
    "pageSize": 10,
    "total": 3,
    "data": [
      {
        "id": 1001,
        "name": "技术研发部",
        "description": "负责产品开发和技术支持",
        "status": "1",
        "createTime": "2025-06-15 09:30:00",
        "createBy": "admin",
        "updateTime": "2025-07-10 14:20:00",
        "updateBy": "admin",
        "delFlag": 0
      },
      {
        "id": 1002,
        "name": "市场部",
        "description": "负责市场推广和品牌宣传",
        "status": "1",
        "createTime": "2025-06-18 10:00:00",
        "createBy": "system",
        "updateTime": "2025-07-15 11:30:00",
        "updateBy": "admin",
        "delFlag": 0
      },
      {
        "id": 1003,
        "name": "财务部",
        "description": "负责公司财务和资金管理",
        "status": "0",
        "createTime": "2025-06-20 08:45:00",
        "createBy": "admin",
        "updateTime": "2025-07-20 13:00:00",
        "updateBy": "system",
        "delFlag": 0
      }
    ]
  }
}




用户团队关联表接口模拟返回：

{
  "code": 1,
  "message": "success",
  "data": {
    "currentPage": 1,
    "pageSize": 10,
    "total": 3,
    "data": [
      {
        "id": 5001,
        "teamId": 1001,
        "userId": 2001,
        "roleId": "admin",
        "joinTime": "2025-07-01 10:00:00",
        "status": "1",
        "createTime": "2025-07-01 09:55:00",
        "createBy": "system",
        "updateTime": "2025-07-15 16:00:00",
        "updateBy": "system"
      },
      {
        "id": 5002,
        "teamId": 1001,
        "userId": 2002,
        "roleId": "member",
        "joinTime": "2025-07-05 14:30:00",
        "status": "1",
        "createTime": "2025-07-05 14:25:00",
        "createBy": "admin",
        "updateTime": "2025-07-20 10:00:00",
        "updateBy": "admin"
      },
      {
        "id": 5003,
        "teamId": 1002,
        "userId": 2003,
        "roleId": "member",
        "joinTime": "2025-07-10 08:00:00",
        "status": "0",
        "createTime": "2025-07-10 07:55:00",
        "createBy": "admin",
        "updateTime": "2025-07-25 12:00:00",
        "updateBy": "admin"
      }
    ]
  }
}


数据权限接口模拟：
{
  "code": 1,
  "message": "success",
  "data": {
    "currentPage": 1,
    "pageSize": 10,
    "total": 2,
    "data": [
      {
        "id": 9001,
        "permissionName": "统计内蒙古极速鲜邮件数量",
        "description": "限定地域与产品，聚合邮件数据",
        "teamId": 1001,
        "datasourceId": 3001,
        "databaseName": "mail_center",
        "schemaName": "public",
        "tableName": "poc_data",
        "conditionExpression": "SELECT SUM(state_mails) FROM (SELECT state_mails, is_deleted, product_name FROM poc_data WHERE unseal_province_name IN ('内蒙古自治区')) AS t WHERE is_deleted = '0' AND product_name = '国内极速鲜'",
        "createTime": "2025-07-10 09:00:00",
        "createBy": "admin",
        "updateTime": "2025-08-01 15:30:00",
        "updateBy": "admin",
        "enable": "1"
      },
      {
        "id": 9002,
        "permissionName": "计算测试表活跃用户数量",
        "description": "嵌套过滤，聚合活跃用户",
        "teamId": 1002,
        "datasourceId": 3002,
        "databaseName": "user_analytics",
        "schemaName": "analysis",
        "tableName": "user_events",
        "conditionExpression": "SELECT COUNT(*) FROM (SELECT user_id, status FROM user_events WHERE event_type = 'login') AS t WHERE status = 'active'",
        "createTime": "2025-07-12 10:15:00",
        "createBy": "system",
        "updateTime": "2025-07-28 11:45:00",
        "updateBy": "system",
        "enable": "1"
      }
    ]
  }
}

角色权限管理接口模拟返回:
{
  "code": 1,
  "message": "success",
  "data": {
    "currentPage": 1,
    "pageSize": 10,
    "total": 2,
    "data": [
      {
        "id": 8001,
        "roleId": 1,
        "permissionId": 9001,
        "teamId": 1001,
        "createTime": "2025-07-15 10:00:00",
        "createBy": "admin",
        "updateTime": "2025-07-20 16:00:00",
        "updateBy": "admin"
      },
      {
        "id": 8002,
        "roleId": 2,
        "permissionId": 9002,
        "teamId": 1002,
        "createTime": "2025-07-16 09:30:00",
        "createBy": "system",
        "updateTime": "2025-07-25 12:45:00",
        "updateBy": "system"
      }
    ]
  }
}
